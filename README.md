# AI 穷人导航 🤖

一个基于 VitePress 构建的精美 AI 穷人导航网站，帮助用户发现和使用最新最有用的免费人工智能工具。

## ✨ 特性

- 🎨 **现代化UI**: 基于 Tailwind CSS 的响应式设计
- 🌙 **深色模式**: 支持明暗主题切换
- 🌍 **多语言支持**: 支持中文/英文双语切换
- 🔍 **智能搜索**: 使用 Fuse.js 进行模糊搜索
- 📱 **响应式**: 完美适配桌面和移动端
- ⚡ **高性能**: VitePress 静态站点生成
- 🏷️ **分类管理**: 按类别组织AI工具
- 📄 **单页面应用**: 所有工具在一个页面展示，无需页面跳转
- 🔗 **详情弹窗**: 支持工具详情页面弹窗展示，用户体验流畅

## 🚀 技术栈

- **框架**: VitePress@latest
- **样式**: Tailwind CSS (JIT 模式)
- **搜索**: Fuse.js
- **构建**: Vite
- **部署**: Cloudflare Pages
- **语言**: TypeScript + Vue 3
- **Markdown解析**: Marked
- **前端元数据**: Gray Matter
- **多语言**: 自定义 i18n 解决方案

## 📁 项目结构

```
deepseekapi/
├── .vitepress/
│   ├── config.ts           # VitePress 配置
│   ├── composables/        # Vue 组合式函数
│   │   ├── useLanguage.ts  # 多语言支持
│   │   └── useMarkdownI18n.ts # Markdown 国际化
│   └── theme/              # 自定义主题
│       ├── index.ts        # 主题入口
│       ├── style.css       # 自定义样式
│       └── components/     # Vue 组件
│           ├── ToolGrid.vue      # 工具网格
│           ├── SearchBox.vue     # 搜索框
│           ├── ThemeToggle.vue   # 主题切换
│           ├── LanguageSwitch.vue # 语言切换
│           └── ToolModal.vue     # 工具详情弹窗
├── data/                   # 数据文件
│   └── tools.ts           # 工具数据定义
├── scripts/               # 构建脚本
│   └── build-search.js    # 搜索索引构建
├── public/                # 静态资源
│   ├── images/            # 图片资源
│   ├── tools/             # 工具详情页面
│   │   ├── chatgpt.md     # 工具详情（中文）
│   │   ├── chatgpt_en.md  # 工具详情（英文）
│   │   └── ...            # 其他工具详情
│   └── logo.svg           # 网站图标
├── en/                    # 英文页面目录（预留）
├── components/            # 组件目录（预留）
├── index.md              # 首页
├── package.json          # 项目依赖和脚本
├── vite.config.ts        # Vite 构建配置
├── tailwind.config.js    # Tailwind CSS 配置
└── postcss.config.js     # PostCSS 配置
```

## 🛠️ 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

### 构建搜索索引

```bash
npm run build:search
```

## 📝 添加新工具

### 1. 在 `data/tools.ts` 中添加工具数据

```typescript
{
  id: 'tool-id',
  title: {
    zh: '工具名称',
    en: 'Tool Name'
  },
  description: {
    zh: '工具描述',
    en: 'Tool Description'
  },
  url: 'https://工具官网',
  tags: {
    zh: ['标签1', '标签2', '标签3'],
    en: ['Tag1', 'Tag2', 'Tag3']
  },
  category: {
    zh: 'AI聊天',
    en: 'AI Chat'
  },
  availability: {
    zh: '免费使用',
    en: 'Free to use'
  },
  isNew: true, // 可选，显示"新增"标签
  detailPath: '/tools/tool-id.md' // 可选，详情页面路径
}
```

### 2. 创建工具详情页面（可选）

在 `public/tools/` 目录下创建 Markdown 文件：

- `tool-id.md` - 中文详情页面
- `tool-id_en.md` - 英文详情页面

### 3. 更新搜索索引

```bash
npm run build:search
```

## 🎨 自定义样式

项目使用 Tailwind CSS，你可以在以下文件中自定义样式：

- `.vitepress/theme/style.css` - 全局样式和 Tailwind 配置
- `tailwind.config.js` - Tailwind 配置文件
- 各组件的 `<style>` 标签 - 组件特定样式

## 🌙 主题切换

项目支持明暗主题切换：

- 自动检测系统偏好
- 记住用户选择（localStorage）
- 平滑过渡动画
- 完整的暗色模式样式

## 🌍 多语言支持

项目支持中文/英文双语：

- **语言切换**: 右上角语言切换按钮，样式与主题切换保持一致
- **自动检测**: 根据浏览器语言自动选择
- **持久化**: 记住用户语言偏好
- **全面覆盖**: UI文本、工具数据（标题、描述、标签、分类）、详情页面全部支持多语言
- **SEO友好**: 动态更新页面标题和描述
- **智能回退**: 如果某种语言的内容不存在，自动回退到中文版本

## 🔍 搜索功能

- **模糊搜索**: 基于 Fuse.js 的智能搜索
- **多字段匹配**: 搜索标题、描述和标签
- **实时建议**: 输入时显示搜索建议
- **键盘快捷键**: Ctrl/Cmd + K 快速聚焦搜索框
- **多语言搜索**: 支持中英文内容搜索

## 📱 响应式设计

- **网格布局**: 自适应不同屏幕尺寸
- **移动优化**: 触摸友好的交互
- **断点设计**: 
  - 手机: 1列
  - 平板: 2列  
  - 桌面: 3-4列

## 🚀 部署到 Cloudflare Pages

1. 将代码推送到 GitHub 仓库
2. 在 Cloudflare Pages 中连接仓库
3. 设置构建命令: `npm run build`
4. 设置输出目录: `.vitepress/dist`
5. 部署完成！

### 环境变量

如需配置额外功能，可设置以下环境变量：

```bash
# 可选：Google Analytics ID
VITE_GA_ID=G-XXXXXXXXXX

# 可选：网站基础 URL
VITE_BASE_URL=https://ai-tools.example.com
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-tool`
3. 提交更改: `git commit -am 'Add new AI tool'`
4. 推送分支: `git push origin feature/new-tool`
5. 提交 Pull Request

### 贡献内容

- 添加新的AI工具
- 改进现有工具描述
- 优化UI/UX设计
- 修复bug和问题
- 完善文档
- 翻译多语言内容

## 📄 许可证

MIT License - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [VitePress](https://vitepress.dev/) - 静态站点生成器
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Fuse.js](https://fusejs.io/) - 模糊搜索库
- [Vue 3](https://v3.vuejs.org/) - 前端框架

---

如果这个项目对你有帮助，请给个 ⭐ Star 支持一下！ 