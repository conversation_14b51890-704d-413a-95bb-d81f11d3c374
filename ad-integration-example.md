# 广告集成示例

这个页面展示了如何在您的网站中集成 Google AdSense 广告。

## 页面顶部横幅广告

<AdUnit 
  type="header" 
  ad-slot="HEADER-AD-SLOT-ID" 
  :width="728" 
  :height="90"
  ad-format="fixed"
  :full-width-responsive="false"
/>

## 内容区域

这里是一些示例内容。广告会根据用户的 Cookie 同意设置自动显示或隐藏。

### 侧边栏广告

<AdUnit 
  type="sidebar" 
  ad-slot="SIDEBAR-AD-SLOT-ID" 
  :width="300" 
  :height="250"
  ad-format="fixed"
  :full-width-responsive="false"
/>

这是更多的内容文本。广告组件使用了懒加载技术，只有当广告区域进入视口时才会加载，这有助于提高页面性能。

## 内容中间的响应式广告

<AdUnit 
  type="content" 
  ad-slot="CONTENT-AD-SLOT-ID" 
  ad-format="auto"
  :full-width-responsive="true"
/>

## 更多内容

这里是更多的示例内容。Cookie 同意横幅会在用户首次访问时显示，用户可以选择接受、拒绝或自定义 Cookie 设置。

## 页面底部广告

<AdUnit 
  type="footer" 
  ad-slot="FOOTER-AD-SLOT-ID" 
  :width="728" 
  :height="90"
  ad-format="fixed"
  :full-width-responsive="false"
/>

## 移动端广告

移动端会显示固定在底部的横幅广告：

<AdUnit 
  type="mobile" 
  ad-slot="MOBILE-AD-SLOT-ID" 
  :width="320" 
  :height="50"
  ad-format="fixed"
  :full-width-responsive="false"
/>

## 使用说明

### 1. 配置 AdSense

在 `.vitepress/config.ts` 中，将以下占位符替换为您的实际值：

- `ca-pub-YOUR-PUBLISHER-ID` → 您的 AdSense 发布商 ID
- `GA_MEASUREMENT_ID` → 您的 Google Analytics 测量 ID

### 2. 配置广告位

在使用 `AdUnit` 组件时，将以下占位符替换为您的实际广告位 ID：

- `HEADER-AD-SLOT-ID`
- `SIDEBAR-AD-SLOT-ID`
- `CONTENT-AD-SLOT-ID`
- `FOOTER-AD-SLOT-ID`
- `MOBILE-AD-SLOT-ID`

### 3. 验证 ads.txt

确保 `public/ads.txt` 文件中的发布商 ID 正确，并且文件可以通过 `https://free.alldsr1.com/ads.txt` 访问。

### 4. 测试 Cookie 同意

Cookie 同意横幅会在用户首次访问时显示。您可以清除浏览器 Cookie 来重新测试。

## 组件属性

### AdUnit 组件属性

- `type`: 广告类型 (`'header' | 'sidebar' | 'content' | 'footer' | 'mobile'`)
- `ad-slot`: 广告位 ID
- `ad-client`: AdSense 客户端 ID (可选，默认使用配置中的值)
- `ad-format`: 广告格式 (`'auto' | 'fixed'`)
- `full-width-responsive`: 是否启用全宽响应式 (布尔值)
- `width`: 广告宽度 (像素)
- `height`: 广告高度 (像素)

### 特性

- **懒加载**: 广告只有在进入视口时才会加载
- **Cookie 同意**: 只有在用户同意广告 Cookie 时才显示广告
- **响应式设计**: 支持移动端和桌面端
- **性能优化**: 使用 Intersection Observer API 进行性能优化
