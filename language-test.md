# 语言检测测试页面

这个页面用于测试语言自动检测和记忆功能。

## 🔍 功能说明

### 自动语言检测
- **首次访问**: 根据浏览器语言自动选择中文或英文
- **智能检测**: 支持检测多种中文变体（zh-CN, zh-TW, zh-HK等）和英文变体
- **优雅降级**: 如果浏览器语言不是中文，默认显示英文

### 用户偏好记忆
- **手动切换**: 用户点击语言切换按钮后，系统会记住这个选择
- **持久化存储**: 使用localStorage保存用户偏好
- **优先级**: 用户手动设置的语言优先级高于浏览器自动检测

## 🧪 测试步骤

### 测试1: 浏览器语言检测
1. 清除浏览器的localStorage数据
2. 设置浏览器语言为中文（zh-CN）
3. 刷新页面，应该显示中文界面
4. 设置浏览器语言为英文（en-US）
5. 清除localStorage并刷新，应该显示英文界面

### 测试2: 用户偏好记忆
1. 在中文界面下，点击语言切换按钮切换到英文
2. 刷新页面，应该保持英文界面（即使浏览器语言是中文）
3. 在英文界面下，切换到中文
4. 刷新页面，应该保持中文界面

### 测试3: 清除偏好重新检测
1. 打开浏览器开发者工具
2. 在Console中执行：`localStorage.removeItem('preferred-language'); localStorage.removeItem('language-user-set')`
3. 刷新页面，应该重新根据浏览器语言进行检测

## 🛠️ 调试工具

打开浏览器开发者工具，在Console中可以使用以下命令：

```javascript
// 查看当前语言设置
console.log('当前语言:', localStorage.getItem('preferred-language'))
console.log('是否用户设置:', localStorage.getItem('language-user-set'))
console.log('浏览器语言:', navigator.language)
console.log('浏览器语言列表:', navigator.languages)

// 清除语言设置
localStorage.removeItem('preferred-language')
localStorage.removeItem('language-user-set')

// 手动设置语言
localStorage.setItem('preferred-language', 'en')
localStorage.setItem('language-user-set', 'true')
```

## 📋 预期行为

| 场景 | 浏览器语言 | localStorage状态 | 预期显示语言 |
|------|------------|------------------|--------------|
| 首次访问 | zh-CN | 空 | 中文 |
| 首次访问 | en-US | 空 | 英文 |
| 用户切换后 | zh-CN | preferred-language=en, language-user-set=true | 英文 |
| 清除设置后 | zh-CN | 空 | 中文 |

## ✅ 检查清单

- [ ] 浏览器语言为中文时，首次访问显示中文
- [ ] 浏览器语言为英文时，首次访问显示英文
- [ ] 用户手动切换语言后，刷新页面保持用户选择
- [ ] 清除localStorage后，重新根据浏览器语言检测
- [ ] 语言切换按钮工作正常
- [ ] 页面标题和描述随语言切换更新
