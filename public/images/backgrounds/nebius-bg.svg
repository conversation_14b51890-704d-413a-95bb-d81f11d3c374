<svg width="600" height="240" viewBox="0 0 600 240" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="nebiusGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="600" height="240" fill="url(#nebiusGradient)"/>
  <circle cx="480" cy="80" r="30" fill="rgba(255,255,255,0.1)"/>
  <circle cx="140" cy="160" r="45" fill="rgba(255,255,255,0.05)"/>
  <rect x="320" y="40" width="4" height="40" rx="2" fill="rgba(255,255,255,0.2)"/>
  <rect x="330" y="50" width="4" height="30" rx="2" fill="rgba(255,255,255,0.15)"/>
  <rect x="340" y="45" width="4" height="35" rx="2" fill="rgba(255,255,255,0.1)"/>
</svg> 