# 图片资源说明

## 📁 目录结构

```
public/images/
├── backgrounds/     # 工具卡片背景图
├── logos/          # 工具图标/Logo
└── README.md       # 本说明文件
```

## 🖼️ 图片规格建议

### 背景图 (backgrounds/)
- **尺寸**: 600×240px (宽×高) 或 2.5:1 比例
- **格式**: JPG/PNG/WebP
- **大小**: 建议 < 200KB
- **内容**: 品牌色彩、抽象图案或产品截图

### Logo图标 (logos/)
- **尺寸**: 128×128px 或更高分辨率正方形
- **格式**: PNG/SVG (推荐SVG)
- **大小**: 建议 < 50KB
- **背景**: 透明或白色背景

## 📝 在工具数据中使用

编辑 `data/tools.ts`，添加图片路径：

```typescript
{
  title: 'ChatGPT',
  description: '最强大的AI对话助手',
  url: 'https://chat.openai.com',
  tags: ['对话', 'GPT'],
  category: 'AI聊天',
  background: '/images/backgrounds/chatgpt-bg.jpg',    // 背景图
  logo: '/images/logos/chatgpt-logo.svg',              // Logo
  availability: '免费使用',
  isHot: true
}
```

## 🔗 路径规则

- ✅ **绝对路径**: `/images/backgrounds/xxx.jpg`
- ✅ **相对路径**: `images/logos/xxx.png` 
- ❌ **不要使用**: `./images/` 或 `../images/`

## 📋 文件命名建议

### 背景图命名
- `chatgpt-bg.jpg`
- `midjourney-bg.png`
- `github-copilot-bg.webp`

### Logo命名
- `chatgpt-logo.svg`
- `midjourney-logo.png`
- `github-logo.svg`

## 🎨 获取图片资源

### 官方Logo
1. 访问工具官网
2. 查找 "Brand Kit" 或 "媒体资源"
3. 下载官方Logo

### 背景图制作
1. **品牌色**: 使用工具的主题色制作渐变
2. **截图**: 产品界面截图作为背景
3. **设计工具**: Figma、Canva等在线工具
4. **AI生成**: 使用Midjourney等AI工具

## ⚡ 默认备用图

如果没有指定图片，系统会使用：
- 背景图: 紫色渐变 (CSS渐变)
- Logo: 工具名称首字母生成

## 🔍 预览效果

添加图片后，卡片效果：
- 顶部显示背景图 (120px高度)
- 左上角叠加Logo (48×48px)
- Logo有白色背景和阴影 