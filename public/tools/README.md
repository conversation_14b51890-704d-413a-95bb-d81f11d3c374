# AI工具详情页面

这个目录存放各个AI工具的详细介绍页面，以Markdown格式编写。

## 📁 目录结构

```
public/tools/
├── README.md              # 本说明文件
├── chatgpt.md            # ChatGPT详情页面
├── midjourney.md         # Midjourney详情页面
├── github-copilot.md     # GitHub Copilot详情页面
└── nebius-studio.md      # Nebius Studio详情页面
```

## 🎯 使用方式

1. **在工具数据中添加路径**: 在 `data/tools.ts` 中为工具添加 `detailPath` 字段
2. **创建详情页面**: 在此目录下创建对应的 `.md` 文件
3. **用户交互**: 用户点击卡片时会弹出详情窗口显示内容

## 📝 页面规范

### 推荐的文档结构
- **工具简介**: 背景和基本介绍
- **主要功能**: 核心功能列表
- **定价方案**: 不同的定价选项
- **使用场景**: 适用的场景和用例
- **使用方法**: 安装和使用步骤
- **相关资源**: 官网、文档等链接

### Markdown语法支持
- 标题 (H1-H3)
- 粗体和斜体
- 链接 (自动在新窗口打开)
- 代码块和行内代码
- 无序列表
- 段落

### 最佳实践
- 使用emoji增强可读性 🎯
- 保持内容简洁明了
- 提供实用的使用建议
- 包含相关的外部链接
- 注意信息的时效性

## 🔄 更新说明

当工具信息发生变化时，请及时更新对应的详情页面，确保用户获得最新、最准确的信息。 