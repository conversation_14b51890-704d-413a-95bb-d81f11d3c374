# 活动结束标记演示页面

这个页面展示了如何为已结束的活动添加相应的标记和样式效果。

## 🎯 功能特性

### 1. 活动结束标签
- **优先级**: "活动结束"标签优先级最高，会覆盖"热门"和"新增"标签
- **多语言**: 中文显示"活动结束"，英文显示"Expired"
- **颜色**: 使用灰色背景 (#6b7280) 表示已结束状态

### 2. 视觉效果
- **整体暗淡**: 卡片透明度降低到 70% (opacity: 0.7)
- **灰度效果**: 添加 30% 的灰度滤镜 (grayscale: 0.3)
- **删除线**: availability 文本添加删除线效果
- **颜色变化**: 删除线文本颜色变为灰色

### 3. 交互效果
- **悬停效果**: 鼠标悬停时透明度提升到 80%
- **点击功能**: 仍然可以点击查看详情或访问网站

## 📝 实现方式

### 数据结构扩展
在 `Tool` 接口中添加了 `isExpired` 字段：

```typescript
export interface Tool {
  // ... 其他字段
  isExpired?: boolean  // 标记活动是否已结束
}
```

### Novita 工具配置
```typescript
{
  id: 'novita',
  title: { zh: 'Novita', en: 'Novita' },
  availability: {
    zh: '💰 注册即送 $10 免费额度 - 立即开始您的 AI 之旅！',
    en: '💰 Sign up for $10 free credits - Start your AI journey now!'
  },
  isExpired: true,  // 标记活动已结束
  // ... 其他配置
}
```

### 样式实现
```css
/* 已过期工具的样式 */
.deal-card.expired {
  opacity: 0.7;
  filter: grayscale(0.3);
}

.deal-card.expired:hover {
  opacity: 0.8;
}

.expired-text {
  text-decoration: line-through;
  color: #9ca3af !important;
}

.card-tag.expired {
  background: #6b7280;
}
```

## 🎨 视觉对比

### 正常状态工具
- ✅ 完整色彩显示
- ✅ 清晰的 availability 文本
- ✅ 热门/新增标签

### 已结束活动工具 (如 Novita)
- 🔘 "活动结束" 灰色标签
- 🔘 整体暗淡效果
- ~~删除线文本~~
- 🔘 灰度滤镜效果

## 🔧 使用方法

要将其他工具标记为活动结束，只需在 `data/tools.ts` 中添加：

```typescript
{
  // ... 工具配置
  isExpired: true,  // 添加这一行
}
```

## 📱 响应式支持

- **桌面端**: 完整的视觉效果和交互
- **移动端**: 保持相同的标记和样式
- **深色模式**: 自动适配深色主题颜色

## 🎯 设计理念

1. **信息透明**: 清楚标示活动状态，避免用户困惑
2. **视觉层次**: 通过颜色和透明度区分活跃和过期内容
3. **用户体验**: 保持可访问性，用户仍可查看详情
4. **一致性**: 在卡片和详情弹窗中保持统一的视觉表现
