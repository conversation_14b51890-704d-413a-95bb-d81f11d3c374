// 多语言文本接口
export interface MultilingualText {
  zh: string
  en: string
}

// 多语言标签接口
export interface MultilingualTags {
  zh: string[]
  en: string[]
}

// 多语言工具接口
export interface Tool {
  id: string  // 工具唯一标识符
  title: MultilingualText
  description: MultilingualText
  url: string
  tags: MultilingualTags
  category: MultilingualText
  background?: string
  logo?: string
  availability?: MultilingualText
  isHot?: boolean
  isNew?: boolean
  isExpired?: boolean  // 标记活动是否已结束
  detailPath?: string  // 详情页面路径，如果提供则显示详情弹窗
}

export const aiTools: Tool[] = [
  {
    id: 'novita',
    title: {
      zh: 'Novita',
      en: 'Novita'
    },
    description: {
      zh: 'Novita 提供 AI 模型部署和 GPU 云计算服务的平台',
      en: 'Novita provides AI model deployment and GPU cloud computing services'
    },
    url: 'https://novita.ai',
    tags: {
      zh: ['GPU', '云计算', '模型服务'],
      en: ['GPU', 'Cloud Computing', 'Model Service']
    },
    category: {
      zh: 'AI API',
      en: 'AI API'
    },
    background: '/images/backgrounds/novita-bg.png',
    logo: 'https://novita.ai/logo/logo.svg',
    availability: {
      zh: '💰 注册即送 $10 免费额度 - 立即开始您的 AI 之旅！',
      en: '💰 Sign up for $10 free credits - Start your AI journey now!'
    },
    isNew: true,
    isExpired: true,  // 标记活动已结束
    detailPath: '/tools/novita.md'
  },
  {
    id: 'bolt-new',
    title: {
      zh: 'Bolt.new',
      en: 'Bolt.new'
    },
    description: {
      zh: 'Bolt.new 提供的黑客马拉松工具包',
      en: 'Hackathon toolkit provided by Bolt.new'
    },
    url: 'https://hackathon.dev/builder-pack',
    tags: {
      zh: ['黑客马拉松', '工具包', '开发平台', '模型服务'],
      en: ['Hackathon', 'Toolkit', 'Development Platform', 'Model Service']
    },
    category: {
      zh: 'AI API',
      en: 'AI API'
    },
    background: '/images/backgrounds/HACKATHONBUILDERPACK.png',
    logo: '/images/logos/bolt.png',
    availability: {
      zh: '🎁 开发大礼包',
      en: '🎁 Developer pack'
    },
    isNew: true,
    detailPath: '/tools/bolt-new.md'
  },
  {
    id: 'chatgpt',
    title: {
      zh: 'ChatGPT',
      en: 'ChatGPT'
    },
    description: {
      zh: '最强大的AI对话助手，支持多轮对话、代码编写、创意写作等',
      en: 'Most powerful AI conversational assistant, supports multi-turn dialogue, code writing, creative writing, etc.'
    },
    url: 'https://chat.openai.com',
    tags: {
      zh: ['对话', 'GPT', '写作', '编程'],
      en: ['Chat', 'GPT', 'Writing', 'Programming']
    },
    category: {
      zh: 'AI聊天',
      en: 'AI Chat'
    },
    background: '/images/backgrounds/chatgpt-bg.svg',
    logo: '/images/logos/chatgpt-logo.svg',
    availability: {
      zh: 'EDU 免费两个月使用',
      en: 'EDU free for 2 months'
    },
    isHot: true,
    detailPath: '/tools/chatgpt.md'
  },
  {
    id: 'github-copilot',
    title: {
      zh: 'GitHub Copilot',
      en: 'GitHub Copilot'
    },
    description: {
      zh: 'AI代码助手，实时代码补全和编程建议',
      en: 'AI code assistant with real-time code completion and programming suggestions'
    },
    url: 'https://github.com/features/copilot',
    tags: {
      zh: ['编程', '代码', 'GitHub', 'VS Code'],
      en: ['Programming', 'Code', 'GitHub', 'VS Code']
    },
    category: {
      zh: 'AI开发',
      en: 'AI Development'
    },
    background: '/images/backgrounds/github-copilot-bg.svg',
    logo: '/images/logos/github-copilot-logo.svg',
    availability: {
      zh: 'EDU 免费使用',
      en: 'EDU free to use'
    },
    isHot: true,
    detailPath: '/tools/github-copilot.md'
  },
  {
    id: 'cursor',
    title: {
      zh: 'Cursor',
      en: 'Cursor'
    },
    description: {
      zh: 'AI 驱动的代码编辑器，智能编程助手',
      en: 'AI-powered code editor, intelligent programming assistant'
    },
    url: 'https://cursor.sh',
    tags: {
      zh: ['编辑器', '编程', 'AI助手'],
      en: ['Editor', 'Programming', 'AI Assistant']
    },
    category: {
      zh: 'AI开发',
      en: 'AI Development'
    },
    background: '/images/backgrounds/cursor-bg.webp',
    logo: '/images/logos/cursor-logo.svg',
    availability: {
      zh: 'EDU 免费一年',
      en: 'EDU free for 1 year'
    },
    isNew: true,
    detailPath: '/tools/cursor.md'
  },
  {
    id: 'gemini',
    title: {
      zh: 'Gemini',
      en: 'Gemini'
    },
    description: {
      zh: 'Google的AI助手，支持多模态交互',
      en: 'Google\'s AI assistant with multimodal interaction support'
    },
    url: 'https://gemini.google.com',
    tags: {
      zh: ['对话', 'Google', '多模态'],
      en: ['Chat', 'Google', 'Multimodal']
    },
    category: {
      zh: 'AI聊天',
      en: 'AI Chat'
    },
    background: '/images/backgrounds/gemini-bg.svg',
    logo: '/images/logos/gemini-logo.svg',
    availability: {
      zh: 'EDU 免费 15 个月',
      en: 'EDU free for 15 months'
    },
    isNew: true,
    detailPath: '/tools/gemini.md'
  },
  {
    id: 'nebius-studio',
    title: {
      zh: 'Nebius Studio',
      en: 'Nebius Studio'
    },
    description: {
      zh: 'API 平台，提供 DeepSeek, Qwen 等模型 API 服务',
      en: 'API platform providing DeepSeek, Qwen and other model API services'
    },
    url: 'https://studio.nebius.com/',
    tags: {
      zh: ['API', 'DeepSeek', '开发平台', '模型服务'],
      en: ['API', 'DeepSeek', 'Development Platform', 'Model Service']
    },
    category: {
      zh: 'AI API',
      en: 'AI API'
    },
    background: '/images/backgrounds/nebius-bg.svg',
    logo: '/images/logos/nebius-logo.svg',
    availability: {
      zh: '💰 $50 额度',
      en: '💰 $50 credits'
    },
    isNew: true,
    detailPath: '/tools/nebius-studio.md'
  }
] 