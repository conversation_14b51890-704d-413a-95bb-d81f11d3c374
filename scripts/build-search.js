import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

// 读取所有工具 markdown 文件
function getAllTools(dir = './tools') {
  const tools = []
  
  function readDir(currentDir) {
    const files = fs.readdirSync(currentDir)
    
    files.forEach(file => {
      const fullPath = path.join(currentDir, file)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        readDir(fullPath)
      } else if (file.endsWith('.md')) {
        const content = fs.readFileSync(fullPath, 'utf8')
        const { data, content: markdown } = matter(content)
        
        if (data.title && data.url) {
          tools.push({
            title: data.title,
            description: data.description || '',
            url: data.url,
            tags: data.tags || [],
            category: data.category || 'Other',
            featured: data.featured || false,
            path: fullPath.replace('./tools/', '/tools/').replace('.md', ''),
            content: markdown.slice(0, 200) // 只保留前200字符作为摘要
          })
        }
      }
    })
  }
  
  try {
    readDir(dir)
  } catch (error) {
    console.warn(`Warning: Could not read directory ${dir}`)
  }
  
  return tools
}

// 构建搜索索引
function buildSearchIndex() {
  console.log('🔍 Building search index...')
  
  const tools = getAllTools()
  
  // 创建输出目录
  const outputDir = './public'
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }
  
  // 写入搜索索引文件
  const searchIndex = {
    tools,
    lastUpdated: new Date().toISOString(),
    total: tools.length
  }
  
  fs.writeFileSync(
    path.join(outputDir, 'search-index.json'),
    JSON.stringify(searchIndex, null, 2)
  )
  
  console.log(`✅ Search index built successfully! Found ${tools.length} tools.`)
  console.log('📄 Categories found:', [...new Set(tools.map(t => t.category))])
  
  return searchIndex
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  buildSearchIndex()
}

export { buildSearchIndex, getAllTools } 