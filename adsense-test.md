# AdSense 测试页面

这是一个用于测试 AdSense 广告集成的页面。

## 🎯 测试说明

1. **Cookie 同意**: 首次访问时会显示 Cookie 同意横幅，请点击"接受"以启用广告
2. **广告显示**: 同意 Cookie 后，下方应该显示 AdSense 广告
3. **控制台检查**: 打开浏览器开发者工具，检查是否有错误信息

## 📊 广告展示区域

### 头部横幅广告
<AdUnit 
  type="header" 
  ad-slot="1234567890" 
  :width="728" 
  :height="90"
/>

### 内容区域广告
<AdUnit 
  type="content" 
  ad-slot="2345678901" 
  ad-format="auto"
  :full-width-responsive="true"
/>

### 侧边栏广告
<AdUnit 
  type="sidebar" 
  ad-slot="3456789012" 
  :width="300" 
  :height="250"
/>

## 🔍 检查清单

- [ ] Cookie 同意横幅正常显示
- [ ] 点击"接受"后横幅消失
- [ ] 广告区域显示"广告"标签
- [ ] 浏览器控制台无错误信息
- [ ] AdSense 脚本正确加载

## 📝 注意事项

1. **广告位ID**: 当前使用的是测试广告位ID，需要在 AdSense 控制台创建真实广告位后替换
2. **审核期间**: AdSense 审核期间可能不会显示广告，这是正常现象
3. **广告填充**: 即使配置正确，广告也可能因为填充率问题而不显示

## 🚀 下一步操作

1. 在 Google AdSense 控制台创建广告位
2. 将测试广告位ID替换为真实ID
3. 等待广告审核通过
4. 监控广告展示和收入数据
