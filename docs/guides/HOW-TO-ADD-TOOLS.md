# 如何添加新的AI工具

## 📝 简单方法（推荐）

只需要编辑一个文件：`data/tools.ts`

### 1. 打开工具数据文件
编辑 `data/tools.ts` 文件

### 2. 在数组中添加新工具
在 `aiTools` 数组的末尾（注释示例之前）添加新工具：

```typescript
{
  id: 'tool-id',
  title: {
    zh: '工具名称',
    en: 'Tool Name'
  },
  description: {
    zh: '工具的简短描述，说明主要功能',
    en: 'Brief description of the tool and its main features'
  },
  url: 'https://工具官网.com',
  tags: {
    zh: ['标签1', '标签2', '标签3'],
    en: ['Tag1', 'Tag2', 'Tag3']
  },
  category: {
    zh: 'AI聊天',
    en: 'AI Chat'
  },
  availability: {
    zh: '免费使用',
    en: 'Free to use'
  },
  isNew: true,      // 可选：显示"新增"标签
  detailPath: '/tools/tool-id.md' // 可选：详情页面路径
},
```

### 3. 字段说明

| 字段 | 必填 | 说明 | 示例 |
|------|------|------|------|
| `id` | ✅ | 工具唯一标识 | `"chatgpt"` |
| `title` | ✅ | 工具名称（多语言） | `{zh: "ChatGPT", en: "ChatGPT"}` |
| `description` | ✅ | 工具描述（多语言） | `{zh: "最强大的AI对话助手", en: "Most powerful AI assistant"}` |
| `url` | ✅ | 工具链接 | `"https://chat.openai.com"` |
| `tags` | ✅ | 搜索标签（多语言） | `{zh: ["对话", "GPT"], en: ["Chat", "GPT"]}` |
| `category` | ✅ | 分类（多语言） | `{zh: "AI聊天", en: "AI Chat"}` |
| `availability` | ❌ | 使用说明（多语言） | `{zh: "免费使用", en: "Free to use"}` |
| `background` | ❌ | 背景图片 | `"/images/bg.jpg"` |
| `logo` | ❌ | Logo图片 | `"/images/logo.png"` |
| `isHot` | ❌ | 热门标签 | `true` |
| `isNew` | ❌ | 新增标签 | `true` |
| `detailPath` | ❌ | 详情页面路径 | `"/tools/example.md"` |

### 4. 可选的分类
- `{zh: "AI聊天", en: "AI Chat"}` - 对话助手、聊天机器人
- `{zh: "AI绘画", en: "AI Image"}` - 图像生成、艺术创作
- `{zh: "AI开发", en: "AI Development"}` - 代码助手、开发工具
- `{zh: "AI写作", en: "AI Writing"}` - 文案生成、内容创作
- `{zh: "AI API", en: "AI API"}` - AI模型API服务、开发平台

## 🎯 实际示例

添加一个新的AI工具：

```typescript
{
  id: 'perplexity',
  title: {
    zh: 'Perplexity',
    en: 'Perplexity'
  },
  description: {
    zh: 'AI搜索引擎，提供准确的答案和引用来源',
    en: 'AI search engine providing accurate answers with citations'
  },
  url: 'https://perplexity.ai',
  tags: {
    zh: ['搜索', '问答', '引用', '研究'],
    en: ['Search', 'Q&A', 'Citations', 'Research']
  },
  category: {
    zh: 'AI聊天',
    en: 'AI Chat'
  },
  availability: {
    zh: '免费使用',
    en: 'Free to use'
  },
  isNew: true
},
```

## 📋 检查清单

添加工具后，请确保：

- [ ] 工具名称准确无误
- [ ] 描述简洁明了（建议不超过30字）
- [ ] URL正确可访问
- [ ] 标签相关且有搜索价值
- [ ] 分类选择正确
- [ ] 逗号和语法正确

## 🔄 不需要重启

保存文件后，开发服务器会自动重新加载，新工具立即显示！

## 📄 详情页面功能

### 弹窗详情显示
如果添加了 `detailPath` 字段，点击工具卡片会弹出详情窗口，而不是直接跳转到外部链接。这样用户可以在不离开网站的情况下了解更多信息。

### 创建详情页面
1. 在 `public/tools/` 目录下创建 `.md` 文件
2. 使用标准 Markdown 格式编写内容
3. 支持多语言：创建 `文件名.md`（中文）和 `文件名_en.md`（英文）
4. 在工具数据中添加 `detailPath: '/tools/文件名.md'`

### 示例详情页面结构
```markdown
# 工具名称 - 简短描述

## 🤖 工具简介
详细介绍工具的背景和用途...

## ✨ 主要功能
列出工具的核心功能...

## 💰 定价方案
不同的定价选项...

## 🎯 使用场景
适用的使用场景...

## 🛠️ 使用方法
安装和使用步骤...

## 🔗 相关资源
相关链接和资源...
```

### 交互逻辑
- **有 `detailPath`**: 点击卡片 → 弹出详情窗口 → 窗口内可点击"访问工具"跳转
- **无 `detailPath`**: 点击卡片 → 直接跳转到外部链接

## 💡 提示

- 优先添加**免费或有免费版本**的工具
- 描述要突出**核心功能**和**使用场景**
- 标签要考虑**用户搜索习惯**
- 新发布的工具可以标记 `isNew: true`
- 非常受欢迎的工具可以标记 `isHot: true` 