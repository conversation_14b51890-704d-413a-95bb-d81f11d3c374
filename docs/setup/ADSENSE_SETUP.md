# Google AdSense 集成设置指南

本指南将帮助您将 Google AdSense 集成到您的 VitePress 网站中。

## 🚀 快速开始

### 1. 获取 AdSense 发布商 ID

1. 登录您的 [Google AdSense 账户](https://www.google.com/adsense/)
2. 转到 **账户** > **账户信息**
3. 复制您的发布商 ID (格式: `ca-pub-xxxxxxxxxxxxxxxxx`)

### 2. 获取 Google Analytics 测量 ID (可选)

1. 登录您的 [Google Analytics 账户](https://analytics.google.com/)
2. 选择您的属性
3. 转到 **管理** > **数据流**
4. 复制测量 ID (格式: `G-XXXXXXXXXX`)

### 3. 配置 VitePress

编辑 `.vitepress/config.ts` 文件，替换以下占位符：

```typescript
// 替换这些值
'ca-pub-YOUR-PUBLISHER-ID' → 'ca-pub-1234567890123456'
'GA_MEASUREMENT_ID' → 'G-XXXXXXXXXX'
```

### 4. 创建广告位

1. 在 AdSense 控制台中创建广告位
2. 获取每个广告位的 ID
3. 在使用 `AdUnit` 组件时替换占位符

### 5. 配置 ads.txt

编辑 `public/ads.txt` 文件：

```
google.com, pub-1234567890123456, DIRECT, f08c47fec0942fa0
```

## 📋 集成清单

### ✅ 已完成的集成

- [x] Google AdSense 脚本集成
- [x] Google Analytics 集成
- [x] Cookie 同意管理
- [x] 响应式广告组件
- [x] 懒加载优化
- [x] SEO 元标签
- [x] 结构化数据
- [x] ads.txt 文件

### 🔧 需要您配置的项目

- [ ] 替换 AdSense 发布商 ID
- [ ] 替换 Google Analytics 测量 ID
- [ ] 创建并配置广告位 ID
- [ ] 测试 Cookie 同意功能
- [ ] 验证 ads.txt 文件访问

## 🎯 使用广告组件

### 基本用法

```vue
<!-- 页面顶部横幅广告 -->
<AdUnit 
  type="header" 
  ad-slot="1234567890" 
  :width="728" 
  :height="90"
/>

<!-- 响应式内容广告 -->
<AdUnit 
  type="content" 
  ad-slot="2345678901" 
  ad-format="auto"
  :full-width-responsive="true"
/>

<!-- 侧边栏广告 -->
<AdUnit 
  type="sidebar" 
  ad-slot="3456789012" 
  :width="300" 
  :height="250"
/>
```

### 广告类型

| 类型 | 描述 | 推荐尺寸 |
|------|------|----------|
| `header` | 页面顶部横幅 | 728x90 |
| `sidebar` | 侧边栏广告 | 300x250 |
| `content` | 内容中广告 | 响应式 |
| `footer` | 页面底部横幅 | 728x90 |
| `mobile` | 移动端固定广告 | 320x50 |

## 🍪 Cookie 同意管理

### 功能特性

- 符合 GDPR 和 CCPA 要求
- 用户可以选择接受、拒绝或自定义设置
- 自动管理 Google Analytics 和 AdSense 同意状态
- 30秒后自动隐藏（如果用户未操作）

### Cookie 类型

1. **必要 Cookie**: 始终启用，网站正常运行必需
2. **分析 Cookie**: 用户可选，用于 Google Analytics
3. **广告 Cookie**: 用户可选，用于 AdSense 广告

## 🔍 测试和验证

### 1. 测试 Cookie 同意

1. 清除浏览器 Cookie
2. 刷新页面
3. 验证 Cookie 同意横幅显示
4. 测试不同的同意选项

### 2. 验证广告显示

1. 确保用户已同意广告 Cookie
2. 检查广告是否正确显示
3. 测试不同设备和屏幕尺寸

### 3. 验证 ads.txt

访问 `https://free.alldsr1.com/ads.txt` 确保文件可访问

### 4. 检查控制台错误

打开浏览器开发者工具，检查是否有 JavaScript 错误

## 🚨 常见问题

### Q: 广告不显示怎么办？

A: 检查以下项目：
- AdSense 发布商 ID 是否正确
- 广告位 ID 是否正确
- 用户是否同意了广告 Cookie
- 网站是否已通过 AdSense 审核

### Q: Cookie 横幅不显示？

A: 可能原因：
- 用户之前已经做过选择
- 清除浏览器 Cookie 重新测试
- 检查浏览器控制台是否有错误

### Q: 如何自定义广告样式？

A: 可以通过 CSS 自定义 `.ad-container` 类的样式

## 📊 性能优化

### 已实现的优化

- **懒加载**: 使用 Intersection Observer API
- **条件加载**: 只有同意 Cookie 才加载广告
- **异步加载**: 所有脚本都是异步加载
- **缓存友好**: 适当的缓存策略

### 建议的额外优化

- 使用 CDN 加速静态资源
- 启用 Gzip 压缩
- 优化图片资源
- 监控 Core Web Vitals

## 📞 支持

如果您在集成过程中遇到问题，请检查：

1. [Google AdSense 帮助中心](https://support.google.com/adsense/)
2. [VitePress 官方文档](https://vitepress.dev/)
3. 浏览器开发者工具中的错误信息

## 🔄 更新日志

- **v1.0.0**: 初始集成完成
  - AdSense 脚本集成
  - Cookie 同意管理
  - 响应式广告组件
  - 性能优化
