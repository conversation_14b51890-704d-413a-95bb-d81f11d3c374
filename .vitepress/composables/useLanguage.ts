import { ref, computed, watch } from 'vue'

export type Language = 'zh' | 'en'

// 简化的翻译接口
export interface SimpleTranslations {
  common: {
    searchPlaceholder: string
    noResults: string
    featured: string
    new: string
    freeToUse: string
    close: string
    visitTool: string
    visitWebsite: string
    loadError: string
  }
  categories: {
    all: string
  }
  theme: {
    toggleLight: string
    toggleDark: string
  }
  language: {
    switchTo: string
  }
  site: {
    title: string
    description: string
    navTitle: string
  }
}

// 翻译数据
const translations: Record<Language, SimpleTranslations> = {
  zh: {
    common: {
      searchPlaceholder: '搜索AI工具...',
      noResults: '没有找到匹配的工具',
      featured: '精选',
      new: '新增',
      freeToUse: '免费使用',
      close: '关闭',
      visitTool: '访问工具',
      visitWebsite: '访问官网',
      loadError: '无法加载详情页面'
    },
    categories: {
      all: '全部'
    },
    theme: {
      toggleLight: '切换到浅色模式',
      toggleDark: '切换到深色模式'
    },
    language: {
      switchTo: '切换语言'
    },
    site: {
      title: 'AI 穷人导航 - 精选免费AI工具集合',
      description: '发现最好的免费AI工具，包括ChatGPT、GitHub Copilot、Cursor等，为学生和开发者提供免费的AI解决方案',
      navTitle: 'AI 穷人导航'
    }
  },
  en: {
    common: {
      searchPlaceholder: 'Search AI tools...',
      noResults: 'No tools found',
      featured: 'Featured',
      new: 'New',
      freeToUse: 'Free to use',
      close: 'Close',
      visitTool: 'Visit Tool',
      visitWebsite: 'Visit Website',
      loadError: 'Failed to load details'
    },
    categories: {
      all: 'All'
    },
    theme: {
      toggleLight: 'Switch to light mode',
      toggleDark: 'Switch to dark mode'
    },
    language: {
      switchTo: 'Switch Language'
    },
    site: {
      title: 'AI Tools Navigator - Curated Free AI Tools Collection',
      description: 'Discover the best free AI tools including ChatGPT, GitHub Copilot, Cursor and more, providing free AI solutions for students and developers',
      navTitle: 'AI Tools Navigator'
    }
  }
}

// 全局语言状态
const currentLanguage = ref<Language>('zh')

// 确保在 SSR 期间也有默认语言
if (typeof window === 'undefined') {
  currentLanguage.value = 'zh'
}

export function useLanguage() {
  // 获取当前翻译
  const t = computed((): SimpleTranslations => {
    return translations[currentLanguage.value]
  })
  
  // 获取当前语言
  const lang = computed(() => currentLanguage.value)
  
  // 切换语言
  const toggleLanguage = () => {
    const newLang: Language = currentLanguage.value === 'zh' ? 'en' : 'zh'
    setLanguage(newLang)
  }
  
  // 设置语言
  const setLanguage = (newLang: Language, isUserAction: boolean = true) => {
    currentLanguage.value = newLang

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', newLang)
      // 标记是否为用户手动设置
      localStorage.setItem('language-user-set', isUserAction.toString())

      // 立即更新页面标题和描述
      setTimeout(() => {
        updatePageMeta()
      }, 0)
    }
  }
  
  // 更新页面元信息
  const updatePageMeta = () => {
    if (typeof window !== 'undefined') {
      const currentT = translations[currentLanguage.value]
      
      // 更新页面标题
      document.title = currentT.site.title
      
      // 更新 meta description
      const metaDescription = document.querySelector('meta[name="description"]')
      if (metaDescription) {
        metaDescription.setAttribute('content', currentT.site.description)
      }
      
      // 更新 html lang 属性
      document.documentElement.lang = currentLanguage.value === 'zh' ? 'zh-CN' : 'en-US'
      
      // 更新导航栏标题文本
      const navBarTitle = document.querySelector('.VPNavBarTitle .title span')
      if (navBarTitle) {
        navBarTitle.textContent = currentT.site.navTitle
      }
      
      // 强制触发标题更新事件
      const titleChangeEvent = new CustomEvent('titlechange', {
        detail: { title: currentT.site.title, lang: currentLanguage.value }
      })
      window.dispatchEvent(titleChangeEvent)
    }
  }
  
  // 检测浏览器语言
  const detectBrowserLanguage = (): Language => {
    if (typeof window === 'undefined') return 'zh'

    // 获取浏览器语言列表，按优先级排序
    const languages = [
      navigator.language,
      ...(navigator.languages || [])
    ].map(lang => lang.toLowerCase())

    // 检查是否有中文语言
    for (const lang of languages) {
      if (lang.startsWith('zh')) {
        return 'zh'
      }
    }

    // 检查是否有英文语言
    for (const lang of languages) {
      if (lang.startsWith('en')) {
        return 'en'
      }
    }

    // 默认返回英文（因为英文是更通用的语言）
    return 'en'
  }

  // 初始化语言设置
  const initLanguage = () => {
    if (typeof window !== 'undefined') {
      // 从 localStorage 读取用户偏好
      const savedLang = localStorage.getItem('preferred-language') as Language
      const isUserPreference = localStorage.getItem('language-user-set') === 'true'

      if (savedLang && (savedLang === 'zh' || savedLang === 'en') && isUserPreference) {
        // 如果是用户手动设置的偏好，直接使用
        currentLanguage.value = savedLang
      } else {
        // 重新检测浏览器语言
        const detectedLang = detectBrowserLanguage()
        setLanguage(detectedLang, false) // 标记为非用户操作
      }

      // 延迟更新，确保DOM已加载
      setTimeout(() => {
        updatePageMeta()
      }, 100)
    } else {
      // SSR 环境下默认使用中文
      currentLanguage.value = 'zh'
    }
  }
  

  
  // 监听语言变化，立即更新页面元信息
  watch(currentLanguage, () => {
    setTimeout(() => {
      updatePageMeta()
    }, 0)
  }, { immediate: false })
  
  return {
    lang,
    t,
    toggleLanguage,
    setLanguage,
    initLanguage
  }
}

// 在应用启动时初始化语言
export function initializeLanguage() {
  const { initLanguage } = useLanguage()
  initLanguage()
}
