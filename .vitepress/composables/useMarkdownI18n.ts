import { computed } from 'vue'
import { useLanguage } from './useLanguage'

export function useMarkdownI18n() {
  const { lang } = useLanguage()
  
  // 获取多语言 Markdown 文件路径
  const getLocalizedPath = (basePath: string): string => {
    if (lang.value === 'en') {
      // 如果是英文，添加 _en 后缀
      const pathParts = basePath.split('.')
      if (pathParts.length > 1) {
        const extension = pathParts.pop()
        const nameWithoutExt = pathParts.join('.')
        return `${nameWithoutExt}_en.${extension}`
      } else {
        return `${basePath}_en`
      }
    }
    
    // 中文使用原始路径
    return basePath
  }
  
  // 检查文件是否存在（这里简化处理，实际项目中可能需要更复杂的逻辑）
  const getAvailablePath = async (basePath: string): Promise<string> => {
    const localizedPath = getLocalizedPath(basePath)
    
    // 在实际应用中，这里可以添加文件存在性检查
    // 现在我们假设如果是英文路径，就使用英文版本，否则使用中文版本
    return localizedPath
  }
  
  return {
    getLocalizedPath,
    getAvailablePath
  }
}
