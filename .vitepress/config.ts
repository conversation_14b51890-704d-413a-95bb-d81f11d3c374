import { defineConfig } from 'vitepress'
import { getCurrentLocaleConfig, generateHeadConfig } from './i18n/config'

// 获取当前语言配置（默认中文）
const getCurrentConfig = () => {
  // 在构建时，可以通过环境变量或其他方式确定语言
  const currentLang = process.env.VITE_LANG || 'zh'
  return getCurrentLocaleConfig(currentLang)
}

const config = getCurrentConfig()

export default defineConfig({
  title: config.title,
  description: config.description,
  lang: config.lang,

  // 忽略死链接检查，因为 LICENSE 文件存在但可能路径问题
  ignoreDeadLinks: true,

  head: generateHeadConfig(config),

  themeConfig: {
    logo: '/logo.svg',
    siteTitle: config.title,
    nav: config.nav,

    // 不需要 sidebar，因为项目是单页面应用
    // 工具通过 ToolGrid 组件展示，详情通过弹窗显示

    socialLinks: [
      // { icon: 'github', link: 'https://github.com' }
    ],

    footer: config.footer
  },

  markdown: {
    theme: {
      light: 'github-light',
      dark: 'github-dark'
    }
  },

  vite: {
    css: {
      postcss: {},
    },
  }
}) 