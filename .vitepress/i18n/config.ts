// 多语言配置管理
export interface LocaleConfig {
  title: string
  description: string
  lang: string
  ogTitle: string
  ogDescription: string
  twitterTitle: string
  twitterDescription: string
  nav: Array<{
    text: string
    link: string
    items?: Array<{ text: string; link: string }>
  }>
  footer: {
    message: string
    copyright: string
  }
  structuredData: any
}

export const localeConfigs: Record<string, LocaleConfig> = {
  zh: {
    title: 'AI 穷人导航',
    description: '精选优质AI工具集合',
    lang: 'zh-CN',
    ogTitle: 'AI 穷人导航 - 精选免费AI工具集合',
    ogDescription: '发现最优质的免费AI工具，涵盖文本生成、图像处理、语音识别等多个领域。',
    twitterTitle: 'AI 穷人导航 - 精选免费AI工具集合',
    twitterDescription: '发现最优质的免费AI工具，涵盖文本生成、图像处理、语音识别等多个领域。',
    nav: [
      { text: '首页', link: '/' },
      { text: '关于', link: '/about' },
      { text: '联系', link: '/contact' },
      {
        text: '更多',
        items: [
          { text: '隐私政策', link: '/privacy' },
          { text: '使用条款', link: '/terms' },
          { text: '广告集成示例', link: '/ad-integration-example' }
        ]
      }
    ],
    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2025 AI 穷人导航'
    },
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "AI 穷人导航",
      "alternateName": "AI Tools Directory",
      "url": "https://free.alldsr1.com/",
      "description": "精选免费AI工具集合，帮助用户发现和使用优质的人工智能工具。",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://free.alldsr1.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AI 穷人导航",
        "url": "https://free.alldsr1.com/"
      }
    }
  },
  en: {
    title: 'AI Tools Navigator',
    description: 'Curated Free AI Tools Collection',
    lang: 'en-US',
    ogTitle: 'AI Tools Navigator - Curated Free AI Tools Collection',
    ogDescription: 'Discover the best free AI tools including text generation, image processing, speech recognition and more.',
    twitterTitle: 'AI Tools Navigator - Curated Free AI Tools Collection',
    twitterDescription: 'Discover the best free AI tools including text generation, image processing, speech recognition and more.',
    nav: [
      { text: 'Home', link: '/en/' },
      { text: 'About', link: '/en/about' },
      { text: 'Contact', link: '/en/contact' },
      {
        text: 'More',
        items: [
          { text: 'Privacy Policy', link: '/en/privacy' },
          { text: 'Terms of Service', link: '/en/terms' },
          { text: 'Ad Integration Example', link: '/en/ad-integration-example' }
        ]
      }
    ],
    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2025 AI Tools Navigator'
    },
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "AI Tools Navigator",
      "alternateName": "AI 穷人导航",
      "url": "https://free.alldsr1.com/en/",
      "description": "Curated free AI tools collection, helping users discover and use high-quality artificial intelligence tools.",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://free.alldsr1.com/en/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "publisher": {
        "@type": "Organization",
        "name": "AI Tools Navigator",
        "url": "https://free.alldsr1.com/en/"
      }
    }
  }
}

// 获取当前语言配置
export const getCurrentLocaleConfig = (lang: string = 'zh'): LocaleConfig => {
  return localeConfigs[lang] || localeConfigs.zh
}

// 生成多语言 head 配置
export const generateHeadConfig = (config: LocaleConfig): Array<[string, Record<string, any>] | [string, Record<string, any>, string]> => {
  const baseUrl = 'https://free.alldsr1.com'
  const isEnglish = config.lang === 'en-US'
  
  return [
    ['meta', { name: 'viewport', content: 'width=device-width, initial-scale=1' }],
    ['meta', { name: 'theme-color', content: '#646cff' }],
    ['link', { rel: 'icon', href: '/favicon.ico' }],

    // SEO and AdSense Meta Tags
    ['meta', { name: 'robots', content: 'index, follow' }],
    ['meta', { name: 'googlebot', content: 'index, follow' }],
    ['meta', { name: 'google-adsense-account', content: 'ca-pub-YOUR-PUBLISHER-ID' }],

    // Open Graph Meta Tags
    ['meta', { property: 'og:title', content: config.ogTitle }],
    ['meta', { property: 'og:description', content: config.ogDescription }],
    ['meta', { property: 'og:type', content: 'website' }],
    ['meta', { property: 'og:url', content: isEnglish ? `${baseUrl}/en/` : `${baseUrl}/` }],
    ['meta', { property: 'og:image', content: `${baseUrl}/og-image.jpg` }],

    // Twitter Card Meta Tags
    ['meta', { name: 'twitter:card', content: 'summary_large_image' }],
    ['meta', { name: 'twitter:title', content: config.twitterTitle }],
    ['meta', { name: 'twitter:description', content: config.twitterDescription }],
    ['meta', { name: 'twitter:image', content: `${baseUrl}/twitter-image.jpg` }],

    // Language alternates for SEO
    ['link', { rel: 'alternate', hreflang: 'zh-CN', href: `${baseUrl}/` }],
    ['link', { rel: 'alternate', hreflang: 'en-US', href: `${baseUrl}/en/` }],
    ['link', { rel: 'alternate', hreflang: 'x-default', href: `${baseUrl}/` }],

    // Google AdSense Auto Ads
    ['script', {
      async: '',
      src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-YOUR-PUBLISHER-ID',
      crossorigin: 'anonymous'
    }],

    // Google Analytics with Consent Management
    ['script', {
      async: '',
      src: 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'
    }],

    // Structured Data for Better SEO
    ['script', {
      type: 'application/ld+json'
    }, JSON.stringify(config.structuredData)]
  ]
}
