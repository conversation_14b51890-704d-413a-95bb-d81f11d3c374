import DefaultTheme from 'vitepress/theme'
import { h } from 'vue'
import './style.css'

// 自定义组件
import ToolGrid from './components/ToolGrid.vue'
import SearchBox from './components/SearchBox.vue'
import LanguageSwitch from './components/LanguageSwitch.vue'
import GoogleAnalytics from './components/GoogleAnalytics.vue'
import AdUnit from './components/AdUnit.vue'
import CookieConsent from './components/CookieConsent.vue'

export default {
  extends: DefaultTheme,
  Layout: () => {
    return h(DefaultTheme.Layout, null, {
      // https://vitepress.dev/guide/extending-default-theme#layout-slots
      'nav-bar-content-after': () => [
        h(LanguageSwitch)
      ],
      'layout-bottom': () => [
        h(GoogleAnalytics),
        h(CookieConsent)
      ],
    })
  },
  enhanceApp({ app }) {
    // 注册全局组件
    app.component('ToolGrid', ToolGrid)
    app.component('SearchBox', SearchBox)
    app.component('LanguageSwitch', LanguageSwitch)
    app.component('GoogleAnalytics', GoogleAnalytics)
    app.component('AdUnit', AdUnit)
    app.component('CookieConsent', CookieConsent)
    
    // 添加全局语言切换监听器
    if (typeof window !== 'undefined') {
      window.addEventListener('titlechange', (event: Event) => {
        // 确保标题更新
        const customEvent = event as CustomEvent
        if (customEvent.detail && customEvent.detail.title) {
          document.title = customEvent.detail.title
          
          // 延迟执行，确保 DOM 已更新
          setTimeout(() => {
            // 再次尝试更新导航栏标题，对于动态加载的组件
            const { lang } = customEvent.detail
            const navBarTitle = document.querySelector('.VPNavBarTitle .title span')
            if (navBarTitle) {
              navBarTitle.textContent = lang === 'zh' ? 'AI 穷人导航' : 'AI Tools Navigator'
            }
          }, 100)
        }
      })
    }
  }
} 