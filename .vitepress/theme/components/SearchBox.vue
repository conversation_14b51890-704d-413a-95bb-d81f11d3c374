<template>
  <div class="search-container">
    <div class="relative">
      <input
        v-model="searchQuery"
        @input="handleSearch"
        @focus="showResults = true"
        @blur="hideResults"
        type="text"
        :placeholder="t.common?.searchPlaceholder || '搜索AI工具...'"
        class="search-input"
      />
      
      <!-- 搜索图标 -->
      <div class="search-icon">
        <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>

      <!-- 清除按钮 -->
      <button
        v-if="searchQuery"
        @click="clearSearch"
        class="clear-btn"
      >
        <svg class="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- 搜索建议 -->
    <div
      v-if="showResults && searchResults.length > 0"
      class="search-results"
    >
      <div
        v-for="(result, index) in searchResults.slice(0, 5)"
        :key="index"
        @mousedown.prevent="selectResult(result.item)"
        class="search-result-item"
      >
        <div class="result-content">
          <h4 class="result-title">{{ result.item.title }}</h4>
          <p class="result-desc">{{ result.item.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import Fuse from 'fuse.js'
import { aiTools, type Tool } from '../../../data/tools'
import { useLanguage } from '../../composables/useLanguage'

const { t } = useLanguage()

const emit = defineEmits<{
  search: [query: string]
}>()

const searchQuery = ref('')
const showResults = ref(false)
const fuse = ref<Fuse<Tool> | null>(null)
const tools = ref<Tool[]>([])

// Fuse.js 配置
const fuseOptions = {
  keys: [
    { name: 'title', weight: 0.7 },
    { name: 'description', weight: 0.2 },
    { name: 'tags', weight: 0.1 }
  ],
  threshold: 0.3,
  distance: 100,
  minMatchCharLength: 1,
  includeScore: true
}

// 搜索结果
const searchResults = computed(() => {
  if (!fuse.value || !searchQuery.value.trim()) {
    return []
  }
  return fuse.value.search(searchQuery.value.trim())
})

// 处理搜索输入
const handleSearch = () => {
  emit('search', searchQuery.value)
  showResults.value = true
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  emit('search', '')
  showResults.value = false
}

// 选择搜索结果
const selectResult = (tool: Tool) => {
  window.open(tool.url, '_blank')
  showResults.value = false
}

// 隐藏搜索结果
const hideResults = () => {
  setTimeout(() => {
    showResults.value = false
  }, 200)
}

// 监听搜索查询变化
watch(searchQuery, (newQuery) => {
  if (!newQuery.trim()) {
    showResults.value = false
  }
})

// 初始化
onMounted(() => {
  // 加载工具数据
  tools.value = aiTools
  
  // 初始化 Fuse.js
  fuse.value = new Fuse(tools.value, fuseOptions)
})

// 键盘快捷键
onMounted(() => {
  const handleKeydown = (e: KeyboardEvent) => {
    // Ctrl/Cmd + K 聚焦搜索框
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault()
      const searchInput = document.querySelector('.search-input') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
      }
    }
    
    // Escape 清除搜索
    if (e.key === 'Escape') {
      clearSearch()
    }
  }

  document.addEventListener('keydown', handleKeydown)
  
  // 清理事件监听器
  return () => {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style scoped>
.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 38px;
  padding: 0 36px 0 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .search-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .search-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.clear-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.2s;
}

.clear-btn:hover {
  background: #f3f4f6;
}

.dark .clear-btn:hover {
  background: #4b5563;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 50;
  max-height: 240px;
  overflow-y: auto;
  margin-top: 4px;
}

.dark .search-results {
  background: #374151;
  border-color: #4b5563;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.search-result-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background 0.2s;
}

.search-result-item:hover {
  background: #f9fafb;
}

.search-result-item:last-child {
  border-bottom: none;
}

.dark .search-result-item {
  border-bottom-color: #4b5563;
}

.dark .search-result-item:hover {
  background: #4b5563;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-title {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
  margin: 0;
}

.dark .result-title {
  color: #f9fafb;
}

.result-desc {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .result-desc {
  color: #9ca3af;
}
</style> 