<template>
  <div class="tool-grid-container">
    <!-- 搜索和分类导航栏 -->
    <div class="header-bar">
      <div class="categories-nav">
        <button
          v-for="category in categories"
          :key="`${lang}-${category}`"
          @click="selectedCategory = category"
          :class="[
            'category-btn',
            selectedCategory === category ? 'active' : ''
          ]"
        >
          {{ category }}
        </button>
      </div>
      <div class="search-wrapper">
        <SearchBox @search="handleSearch" />
      </div>
    </div>

    <!-- 工具网格 -->
    <div class="deals-grid">
      <div
        v-for="tool in filteredTools"
        :key="tool.title"
        :class="['deal-card', { 'expired': tool.isExpired }]"
        @click="handleCardClick(tool)"
      >
        <!-- 卡片标签 -->
        <div v-if="tool.isExpired" class="card-tag expired">{{ lang === 'zh' ? '活动结束' : 'Expired' }}</div>
        <div v-else-if="tool.isHot" class="card-tag hot">{{ t.common?.featured || '精选' }}</div>
        <div v-else-if="tool.isNew" class="card-tag new">{{ t.common?.new || '新增' }}</div>
        
        <!-- 背景图 -->
        <div class="card-image">
          <img :src="tool.background || '/default-bg.jpg'" :alt="tool.title + ' 背景'">
        </div>
        
        <!-- Logo - 支持LobeHub图标CDN或自定义图片 -->
        <div class="card-logo">
          <!-- 使用LobeHub图标CDN（支持深色模式） -->
          <picture v-if="getLobeHubIconUrl(getToolTitle(tool))" class="lobehub-icon">
            <source
              media="(prefers-color-scheme: dark)"
              :srcset="getLobeHubIconUrl(getToolTitle(tool))?.dark"
            >
            <img
              :src="getLobeHubIconUrl(getToolTitle(tool))?.light"
              :alt="getToolTitle(tool)"
              class="lobehub-icon-img"
            >
          </picture>
          <!-- 回退到自定义图片 -->
          <img 
            v-else 
            :src="tool.logo || '/default-logo.svg'" 
            :alt="tool.title"
          >
        </div>
        
        <!-- 内容 -->
        <div class="card-content">
          <div class="card-brand">{{ getToolTitle(tool).toUpperCase() }}</div>
          <h3 class="card-title">{{ getToolDescription(tool) }}</h3>
          <div :class="['card-availability', { 'expired-text': tool.isExpired }]">{{ getToolAvailability(tool) }}</div>
        </div>

        <!-- 详情指示器 -->
        <div v-if="tool.detailPath" class="detail-indicator">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="filteredTools.length === 0" class="empty-state">
      <div class="text-gray-400 dark:text-gray-600 text-lg mb-2">😅</div>
      <p class="text-gray-600 dark:text-gray-400">{{ t.common?.noResults || '没有找到匹配的工具' }}</p>
    </div>

    <!-- 工具详情弹窗 -->
    <ToolModal 
      :tool="selectedTool" 
      :isVisible="isModalVisible" 
      @close="closeModal" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import SearchBox from './SearchBox.vue'
import ToolModal from './ToolModal.vue'
import { aiTools, type Tool } from '../../../data/tools'
import { useLanguage } from '../../composables/useLanguage'

const { lang, t } = useLanguage()

const tools = ref<Tool[]>([])
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedTool = ref<Tool | null>(null)
const isModalVisible = ref(false)

// 获取工具翻译内容的函数
const getToolTitle = (tool: Tool): string => {
  return tool.title[lang.value] || tool.title.zh
}

const getToolDescription = (tool: Tool): string => {
  return tool.description[lang.value] || tool.description.zh
}

const getToolCategory = (tool: Tool): string => {
  return tool.category[lang.value] || tool.category.zh
}

const getToolAvailability = (tool: Tool): string => {
  if (tool.availability) {
    return tool.availability[lang.value] || tool.availability.zh
  }
  return t.value.common?.freeToUse || '免费使用'
}

const getToolTags = (tool: Tool): string[] => {
  return tool.tags[lang.value] || tool.tags.zh
}

// 获取所有分类 - 响应式地依赖于语言变化
const categories = computed(() => {
  const allText = t.value.categories?.all || '全部'
  const uniqueCategories = new Set(tools.value.map(tool => getToolCategory(tool)))
  return [allText, ...Array.from(uniqueCategories)]
})

// 获取当前"全部"文字
const getAllText = computed(() => {
  return t.value.categories?.all || '全部'
})

// 初始化选中分类
const initSelectedCategory = () => {
  selectedCategory.value = getAllText.value
}

// 过滤工具
const filteredTools = computed(() => {
  let filtered = tools.value

  // 分类筛选
  const allText = getAllText.value
  if (selectedCategory.value !== allText) {
    filtered = filtered.filter(tool => getToolCategory(tool) === selectedCategory.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tool =>
      getToolTitle(tool).toLowerCase().includes(query) ||
      getToolDescription(tool).toLowerCase().includes(query) ||
      getToolTags(tool).some(tag => tag.toLowerCase().includes(query))
    )
  }

  return filtered
})

// 处理搜索
const handleSearch = (query: string) => {
  searchQuery.value = query
}

// 处理卡片点击
const handleCardClick = (tool: Tool) => {
  if (tool.detailPath) {
    // 有详情页面，显示弹窗
    selectedTool.value = tool
    isModalVisible.value = true
  } else {
    // 没有详情页面，直接跳转
    window.open(tool.url, '_blank', 'noopener,noreferrer')
  }
}

// 关闭弹窗
const closeModal = () => {
  isModalVisible.value = false
  selectedTool.value = null
}

// 监听语言变化，重新初始化选中分类
watch(() => lang.value, () => {
  // 如果当前选中的是"全部"，则更新为新语言的"全部"
  const oldAllText = selectedCategory.value
  const isAllCategory = oldAllText === '全部' || oldAllText === 'All'
  
  if (isAllCategory) {
    initSelectedCategory()
  }
}, { immediate: false })

// 加载工具数据
onMounted(() => {
  tools.value = aiTools
  // 初始化选中分类
  initSelectedCategory()
})

// 获取LobeHub图标CDN URL
const getLobeHubIconUrl = (toolTitle: string) => {
  const iconMap: Record<string, string> = {
    'ChatGPT': 'openai',
    'Cursor': 'cursor',
    'GitHub Copilot': 'githubcopilot',
    'Gemini': 'gemini',
    'Nebius Studio': 'nebius'
  }
  
  const iconSlug = iconMap[toolTitle]
  if (!iconSlug) return null
  
  // 使用阿里云镜像的CDN服务，支持深色模式
  return {
    light: `https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/${iconSlug}.png`,
    dark: `https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/${iconSlug}.png`
  }
}
</script>

<style scoped>
.tool-grid-container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.categories-nav {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.category-btn:hover {
  background: #e5e7eb;
}

.category-btn.active {
  background: #3b82f6;
  color: white;
}

.dark .category-btn {
  background: #374151;
  color: #d1d5db;
}

.dark .category-btn:hover {
  background: #4b5563;
}

.dark .category-btn.active {
  background: #2563eb;
}

.search-wrapper {
  min-width: 280px;
}

.deals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.deal-card {
  position: relative;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: block;
  height: 300px;
}

.deal-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.dark .deal-card {
  background: #1f2937;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .deal-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.card-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  z-index: 10;
  color: white;
}

.card-tag.hot {
  background: #ef4444;
}

.card-tag.new {
  background: #10b981;
}

.card-tag.expired {
  background: #6b7280;
}

.card-image {
  height: 120px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-logo {
  position: absolute;
  top: 80px;
  left: 16px;
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* LobeHub图标样式 */
.lobehub-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.lobehub-icon-img,
.lobehub-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.card-logo img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.card-content {
  padding: 24px 16px 16px;
  height: calc(100% - 120px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-brand {
  font-size: 11px;
  font-weight: 700;
  color: #6b7280;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.dark .card-brand {
  color: #9ca3af;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  line-height: 1.5;
  margin-bottom: 12px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 45px;
}

.dark .card-title {
  color: #f9fafb;
}

.card-availability {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.dark .card-availability {
  color: #9ca3af;
}

/* 已过期工具的样式 */
.deal-card.expired {
  opacity: 0.7;
  filter: grayscale(0.3);
}

.deal-card.expired:hover {
  opacity: 0.8;
}

.expired-text {
  text-decoration: line-through;
  color: #9ca3af !important;
}

.dark .expired-text {
  color: #6b7280 !important;
}

.detail-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 10;
}

.empty-state {
  text-align: center;
  padding: 48px 0;
}

@media (max-width: 768px) {
  .header-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-wrapper {
    min-width: auto;
  }
  
  .deals-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .deal-card {
    height: 240px;
  }
  
  .card-image {
    height: 100px;
  }
  
  .card-logo {
    top: 70px;
    width: 40px;
    height: 40px;
  }
  
  .card-logo img {
    width: 24px;
    height: 24px;
  }
  
  .card-content {
    height: calc(100% - 100px);
    padding: 20px 12px 12px;
  }
  
  .card-title {
    line-height: 1.4;
    min-height: 36px;
  }
  
  .lobehub-icon-img,
  .lobehub-icon img {
    width: 24px;
    height: 24px;
  }
}
</style> 