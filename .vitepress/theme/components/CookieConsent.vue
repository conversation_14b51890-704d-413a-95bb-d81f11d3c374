<template>
  <!-- <PERSON><PERSON>sent Banner -->
  <Transition name="slide-up">
    <div 
      v-if="showBanner" 
      class="cookie-consent-banner"
    >
      <div class="cookie-consent-content">
        <div class="cookie-consent-text">
          <span class="cookie-icon">🍪</span>
          <span class="cookie-message">
            我们使用 Cookie 来改善您的浏览体验、提供个性化内容和分析网站流量。继续使用本网站即表示您同意我们的 
            <a href="/privacy/" target="_blank">隐私政策</a>。
          </span>
        </div>
        <div class="cookie-consent-buttons">
          <button @click="acceptAll" class="cookie-btn cookie-btn-accept">接受</button>
          <button @click="declineAll" class="cookie-btn cookie-btn-decline">拒绝</button>
          <button @click="showSettings" class="cookie-btn cookie-btn-settings">设置</button>
        </div>
      </div>
    </div>
  </Transition>

  <!-- <PERSON><PERSON> Settings Modal -->
  <Transition name="modal">
    <div 
      v-if="showModal" 
      class="cookie-modal"
      @click="hideModal"
    >
      <div class="cookie-modal-content" @click.stop>
        <div class="cookie-modal-header">
          <h3>Cookie 设置</h3>
          <button @click="hideModal" class="cookie-modal-close">&times;</button>
        </div>
        <div class="cookie-modal-body">
          <div class="cookie-category">
            <div class="cookie-category-header">
              <h4>必要 Cookie</h4>
              <label class="cookie-switch">
                <input type="checkbox" checked disabled>
                <span class="cookie-slider"></span>
              </label>
            </div>
            <p>这些 Cookie 是网站正常运行所必需的，无法禁用。</p>
          </div>
          
          <div class="cookie-category">
            <div class="cookie-category-header">
              <h4>分析 Cookie</h4>
              <label class="cookie-switch">
                <input 
                  type="checkbox" 
                  v-model="preferences.analytics"
                >
                <span class="cookie-slider"></span>
              </label>
            </div>
            <p>帮助我们了解访问者如何使用网站，以改进用户体验。</p>
          </div>
          
          <div class="cookie-category">
            <div class="cookie-category-header">
              <h4>广告 Cookie</h4>
              <label class="cookie-switch">
                <input 
                  type="checkbox" 
                  v-model="preferences.advertising"
                >
                <span class="cookie-slider"></span>
              </label>
            </div>
            <p>用于展示相关广告和衡量广告效果。</p>
          </div>
        </div>
        <div class="cookie-modal-footer">
          <button @click="saveSettings" class="cookie-btn cookie-btn-accept">保存设置</button>
          <button @click="acceptAllFromModal" class="cookie-btn cookie-btn-accept">接受全部</button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const showBanner = ref(false)
const showModal = ref(false)
const preferences = ref({
  analytics: false,
  advertising: false
})

// Cookie utility functions
function setCookie(name: string, value: string, days: number) {
  const expires = new Date()
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`
}

function getCookie(name: string): string | null {
  const nameEQ = name + '='
  const ca = document.cookie.split(';')
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1, c.length)
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
  }
  return null
}

// Update Google Analytics consent
function updateGoogleAnalytics(enabled: boolean) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    ;(window as any).gtag('consent', 'update', {
      'analytics_storage': enabled ? 'granted' : 'denied'
    })
  }
}

// Update AdSense consent
function updateAdSenseConsent(enabled: boolean) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    ;(window as any).gtag('consent', 'update', {
      'ad_storage': enabled ? 'granted' : 'denied'
    })
  }
}

// Load user preferences
function loadPreferences() {
  const consent = getCookie('cookie-consent')
  if (consent === 'custom') {
    preferences.value.analytics = getCookie('analytics-consent') === 'true'
    preferences.value.advertising = getCookie('advertising-consent') === 'true'
  } else if (consent === 'accepted') {
    preferences.value.analytics = true
    preferences.value.advertising = true
  }
}

// Accept all cookies
function acceptAll() {
  setCookie('cookie-consent', 'accepted', 365)
  setCookie('analytics-consent', 'true', 365)
  setCookie('advertising-consent', 'true', 365)
  updateGoogleAnalytics(true)
  updateAdSenseConsent(true)
  showBanner.value = false
}

// Decline all cookies
function declineAll() {
  setCookie('cookie-consent', 'declined', 365)
  setCookie('analytics-consent', 'false', 365)
  setCookie('advertising-consent', 'false', 365)
  updateGoogleAnalytics(false)
  updateAdSenseConsent(false)
  showBanner.value = false
}

// Show settings modal
function showSettings() {
  loadPreferences()
  showModal.value = true
}

// Hide settings modal
function hideModal() {
  showModal.value = false
}

// Save custom settings
function saveSettings() {
  setCookie('cookie-consent', 'custom', 365)
  setCookie('analytics-consent', preferences.value.analytics.toString(), 365)
  setCookie('advertising-consent', preferences.value.advertising.toString(), 365)
  
  updateGoogleAnalytics(preferences.value.analytics)
  updateAdSenseConsent(preferences.value.advertising)
  
  showModal.value = false
  showBanner.value = false
}

// Accept all from modal
function acceptAllFromModal() {
  preferences.value.analytics = true
  preferences.value.advertising = true
  saveSettings()
}

onMounted(() => {
  // Check if user has already made a choice
  const cookieConsent = getCookie('cookie-consent')
  
  if (!cookieConsent) {
    // Show banner after a short delay
    setTimeout(() => {
      showBanner.value = true
    }, 1500)
    
    // Auto-hide banner after 30 seconds if no interaction
    setTimeout(() => {
      if (showBanner.value) {
        showBanner.value = false
        setCookie('cookie-consent', 'dismissed', 7)
      }
    }, 30000)
  } else {
    // Load existing preferences
    loadPreferences()
    if (cookieConsent === 'accepted') {
      updateGoogleAnalytics(true)
      updateAdSenseConsent(true)
    } else if (cookieConsent === 'custom') {
      updateGoogleAnalytics(preferences.value.analytics)
      updateAdSenseConsent(preferences.value.advertising)
    }
  }
})
</script>

<style scoped>
/* Transitions */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease-out;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

/* Cookie Consent Banner Styles */
.cookie-consent-banner {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid var(--vp-c-divider);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  padding: 20px;
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin: 0 auto;
  font-family: var(--vp-font-family-base);
}

@media (min-width: 768px) {
  .cookie-consent-banner {
    left: auto;
    right: 20px;
    max-width: 450px;
  }
}

.cookie-consent-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

@media (min-width: 576px) {
  .cookie-consent-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.cookie-consent-text {
  font-size: 14px;
  line-height: 1.4;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--vp-c-text-1);
}

.cookie-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.cookie-message a {
  color: var(--vp-c-brand-1);
  text-decoration: none;
  font-weight: 500;
}

.cookie-message a:hover {
  text-decoration: underline;
}

.cookie-consent-buttons {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  flex-wrap: wrap;
}

@media (max-width: 575px) {
  .cookie-consent-buttons {
    justify-content: center;
  }
}

.cookie-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.cookie-btn-accept {
  background-color: var(--vp-c-brand-1);
  color: white;
}

.cookie-btn-accept:hover {
  background-color: var(--vp-c-brand-2);
}

.cookie-btn-decline {
  background-color: var(--vp-c-bg-soft);
  color: var(--vp-c-text-2);
  border: 1px solid var(--vp-c-divider);
}

.cookie-btn-decline:hover {
  background-color: var(--vp-c-bg-mute);
}

.cookie-btn-settings {
  background-color: transparent;
  color: var(--vp-c-brand-1);
  border: 1px solid var(--vp-c-brand-1);
}

.cookie-btn-settings:hover {
  background-color: var(--vp-c-brand-1);
  color: white;
}

/* Cookie Modal Styles */
.cookie-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.cookie-modal-content {
  background: var(--vp-c-bg);
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  font-family: var(--vp-font-family-base);
  border: 1px solid var(--vp-c-divider);
}

.cookie-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
  border-bottom: 1px solid var(--vp-c-divider);
  margin-bottom: 20px;
}

.cookie-modal-header h3 {
  margin: 0;
  color: var(--vp-c-text-1);
}

.cookie-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--vp-c-text-2);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cookie-modal-body {
  padding: 0 20px;
}

.cookie-category {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--vp-c-divider-light);
}

.cookie-category:last-child {
  border-bottom: none;
}

.cookie-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cookie-category h4 {
  margin: 0;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.cookie-category p {
  margin: 0;
  color: var(--vp-c-text-2);
  font-size: 14px;
  line-height: 1.4;
}

/* Cookie Switch Styles */
.cookie-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.cookie-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.cookie-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vp-c-bg-mute);
  transition: 0.2s;
  border-radius: 24px;
}

.cookie-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

input:checked + .cookie-slider {
  background-color: var(--vp-c-brand-1);
}

input:checked + .cookie-slider:before {
  transform: translateX(26px);
}

input:disabled + .cookie-slider {
  background-color: var(--vp-c-brand-1);
  opacity: 0.6;
  cursor: not-allowed;
}

.cookie-modal-footer {
  padding: 20px;
  border-top: 1px solid var(--vp-c-divider);
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Dark mode support */
.dark .cookie-consent-banner {
  background: rgba(30, 30, 30, 0.98);
}

.dark .cookie-slider:before {
  background-color: var(--vp-c-bg);
}
</style>
