<template>
  <button
    @click="toggleLanguage"
    class="VPSwitch VPSwitchAppearance language-switch-btn"
    type="button"
    role="switch"
    :title="t.language?.switchTo || '切换语言'"
    :aria-checked="lang === 'en'"
  >
    <span class="check">
      <span class="icon">
        <span class="language-text zh">中</span>
        <span class="language-text en">EN</span>
      </span>
    </span>
  </button>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useLanguage } from '../../composables/useLanguage'

const { lang, t, toggleLanguage, initLanguage } = useLanguage()

// 在客户端初始化语言
onMounted(() => {
  initLanguage()
})
</script>

<style scoped>
/* 使用VitePress原生开关样式变量 */
.VPSwitch {
  position: relative;
  border-radius: 11px;
  display: block;
  width: 40px;
  height: 22px;
  flex-shrink: 0;
  border: 1px solid var(--vp-input-border-color);
  background-color: var(--vp-input-switch-bg-color);
  transition: border-color 0.25s !important;
}

/* 给语言切换按钮添加右边距 */
.language-switch-btn {
  margin-right: 16px; /* 增加PC端间距 */
}

.VPSwitch:hover {
  border-color: var(--vp-c-brand-1);
}

.check {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: var(--vp-c-neutral-inverse);
  box-shadow: var(--vp-shadow-1);
  transition: transform 0.25s !important;
}

/* 英文状态时，滑块移动到右侧 */
.VPSwitch[aria-checked="true"] .check {
  transform: translateX(18px);
}

.icon {
  position: relative;
  display: block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  overflow: hidden;
}

/* 语言文字样式，模仿主题切换的太阳/月亮图标效果 */
.language-text {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 12px;
  height: 12px;
  font-size: 9px;
  font-weight: 700;
  line-height: 12px;
  text-align: center;
  color: var(--vp-c-text-2);
  transition: opacity 0.25s !important;
  user-select: none;
}

/* 中文显示逻辑：中文时显示，英文时隐藏 */
.language-text.zh {
  opacity: 1;
}

.language-text.en {
  opacity: 0;
}

/* 英文状态时的显示逻辑 */
.VPSwitch[aria-checked="true"] .language-text.zh {
  opacity: 0;
}

.VPSwitch[aria-checked="true"] .language-text.en {
  opacity: 1;
}

/* 深色模式下的文字颜色 */
.dark .language-text {
  color: var(--vp-c-text-1);
}

.language-text.en {
  font-size: 7px;
  font-weight: 800;
  letter-spacing: 0.2px;
}

.language-text.zh {
  font-size: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-switch-btn {
    margin-right: 10px; /* 移动端稍小间距 */
  }
}
</style>
