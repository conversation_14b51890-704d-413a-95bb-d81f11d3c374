<template>
  <div 
    v-if="shouldShowAd" 
    :class="['ad-container', `ad-${type}`]"
    ref="adContainer"
  >
    <div class="ad-label">广告</div>
    <ins 
      class="adsbygoogle"
      :style="adStyle"
      :data-ad-client="adClient"
      :data-ad-slot="adSlot"
      :data-ad-format="adFormat"
      :data-full-width-responsive="fullWidthResponsive"
    ></ins>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'

interface Props {
  type: 'header' | 'sidebar' | 'content' | 'footer' | 'mobile'
  adSlot: string
  adClient?: string
  adFormat?: string
  fullWidthResponsive?: boolean
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  adClient: 'ca-pub-6469223357020423',
  adFormat: 'auto',
  fullWidthResponsive: true,
  width: 300,
  height: 250
})

const adContainer = ref<HTMLElement>()
const shouldShowAd = ref(false)

const adStyle = computed(() => {
  if (props.adFormat === 'auto') {
    return 'display:block'
  }
  return `display:inline-block;width:${props.width}px;height:${props.height}px`
})

// Check if ads should be shown based on consent
function checkAdConsent(): boolean {
  if (typeof window === 'undefined') return false
  
  function getCookie(name: string): string | null {
    const nameEQ = name + '='
    const ca = document.cookie.split(';')
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
    }
    return null
  }
  
  const cookieConsent = getCookie('cookie-consent')
  const advertisingConsent = getCookie('advertising-consent')
  
  return cookieConsent === 'accepted' || advertisingConsent === 'true'
}

// Initialize ad when component is mounted and visible
onMounted(async () => {
  if (!checkAdConsent()) {
    return
  }
  
  await nextTick()
  
  // Use Intersection Observer for lazy loading
  if (adContainer.value && 'IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !shouldShowAd.value) {
          shouldShowAd.value = true
          observer.disconnect()
          
          // Initialize AdSense
          nextTick(() => {
            if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
              try {
                ;((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({})
              } catch (e) {
                console.warn('AdSense initialization failed:', e)
              }
            }
          })
        }
      })
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    })
    
    observer.observe(adContainer.value)
  } else {
    // Fallback for browsers without Intersection Observer
    shouldShowAd.value = true
    nextTick(() => {
      if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
        try {
          ;((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({})
        } catch (e) {
          console.warn('AdSense initialization failed:', e)
        }
      }
    })
  }
})
</script>

<style scoped>
.ad-container {
  margin: 20px 0;
  text-align: center;
  clear: both;
  position: relative;
}

.ad-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
  text-transform: uppercase;
  font-weight: 500;
}

.ad-header {
  margin-bottom: 30px;
}

.ad-sidebar {
  float: right;
  margin: 0 0 20px 20px;
}

.ad-content {
  margin: 30px 0;
  padding: 20px 0;
  border-top: 1px solid var(--vp-c-divider);
  border-bottom: 1px solid var(--vp-c-divider);
}

.ad-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--vp-c-divider);
}

.ad-mobile {
  display: none;
}

/* Mobile responsive ads */
@media (max-width: 768px) {
  .ad-header,
  .ad-footer {
    display: none;
  }
  
  .ad-mobile {
    display: block;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--vp-c-bg);
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 10px;
  }
  
  .ad-sidebar {
    float: none;
    margin: 20px 0;
  }
}

/* Dark mode support */
.dark .ad-mobile {
  background: var(--vp-c-bg);
  box-shadow: 0 -2px 10px rgba(255,255,255,0.1);
}
</style>
