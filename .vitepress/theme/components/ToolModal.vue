<template>
  <Teleport to="body">
    <div
      v-if="isVisible"
      class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
      @click.self="closeModal"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <img
              v-if="tool?.logo"
              :src="tool.logo"
              :alt="tool.title"
              class="w-8 h-8 rounded object-contain"
            >
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ getToolTitle(tool) }}
            </h2>
          </div>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Modal Content -->
        <div class="overflow-y-auto flex-1 min-h-0">
          <div v-if="loading" class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
          
          <div v-else-if="error" class="p-6 text-center">
            <p class="text-red-600 dark:text-red-400 mb-4">{{ error }}</p>
            <button
              @click="visitTool"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              {{ t.common?.visitWebsite || '访问官网' }}
            </button>
          </div>

          <div v-else class="prose prose-slate dark:prose-invert max-w-none p-6 pb-8" v-html="renderedContent"></div>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
          <div class="flex items-center space-x-4">
            <span v-if="tool?.availability" class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm rounded-full">
              {{ getToolAvailability(tool) }}
            </span>
            <div class="flex space-x-2">
              <span v-if="tool?.isHot" class="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs rounded-full">
                {{ t.common?.featured || '热门' }}
              </span>
              <span v-if="tool?.isNew" class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
                {{ t.common?.new || '新增' }}
              </span>
            </div>
          </div>
          <div class="flex space-x-3">
            <button
              @click="closeModal"
              class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              {{ t.common?.close || '关闭' }}
            </button>
            <button
              @click="visitTool"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              {{ t.common?.visitTool || '访问工具' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import { marked } from 'marked'
import type { Tool } from '../../../data/tools'
import { useLanguage } from '../../composables/useLanguage'
import { useMarkdownI18n } from '../../composables/useMarkdownI18n'

const { lang, t } = useLanguage()
const { getLocalizedPath } = useMarkdownI18n()

interface Props {
  tool: Tool | null
  isVisible: boolean
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const error = ref('')
const renderedContent = ref('')

// 获取工具多语言信息的函数
const getToolTitle = (tool: Tool | null): string => {
  if (!tool) return ''
  return tool.title[lang.value] || tool.title.zh
}

const getToolAvailability = (tool: Tool | null): string => {
  if (!tool?.availability) return ''
  return tool.availability[lang.value] || tool.availability.zh
}

// 配置marked选项
marked.setOptions({
  breaks: true,
  gfm: true,
  headerIds: false,
  mangle: false
})

async function loadContent() {
  if (!props.tool?.detailPath) return

  loading.value = true
  error.value = ''

  try {
    // 获取多语言文件路径
    const localizedPath = getLocalizedPath(props.tool.detailPath)

    // 首先尝试加载多语言版本
    let response = await fetch(localizedPath)

    // 如果多语言版本不存在，回退到默认版本
    if (!response.ok && lang.value === 'en') {
      response = await fetch(props.tool.detailPath)
    }

    if (!response.ok) {
      throw new Error(t.common?.loadError || '无法加载详情页面')
    }
    
    const markdown = await response.text()
    // 使用marked渲染markdown
    let html = await marked.parse(markdown)
    
    // 后处理：添加自定义样式
    html = html
      // 行内代码样式
      .replace(/<code>/g, '<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">')
      // 代码块样式  
      .replace(/<pre><code>/g, '<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto"><code>')
      // 链接样式
      .replace(/<a href="/g, '<a href="')
      .replace(/<a /g, '<a class="text-blue-600 dark:text-blue-400 hover:underline" target="_blank" rel="noopener noreferrer" ')
      // 标题样式
      .replace(/<h1>/g, '<h1 class="text-2xl font-bold mb-4 mt-6">')
      .replace(/<h2>/g, '<h2 class="text-xl font-semibold mb-3 mt-5">')
      .replace(/<h3>/g, '<h3 class="text-lg font-medium mb-2 mt-4">')
      .replace(/<h4>/g, '<h4 class="text-base font-medium mb-2 mt-3">')
      // 段落样式
      .replace(/<p>/g, '<p class="mb-4 leading-relaxed">')
      // 列表样式
      .replace(/<ul>/g, '<ul class="list-disc list-inside space-y-1 ml-4 mb-4">')
      .replace(/<ol>/g, '<ol class="list-decimal list-inside space-y-1 ml-4 mb-4">')
    
    renderedContent.value = html
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载失败'
  } finally {
    loading.value = false
  }
}

function closeModal() {
  emit('close')
}

function visitTool() {
  if (props.tool?.url) {
    window.open(props.tool.url, '_blank', 'noopener,noreferrer')
  }
}

// 监听弹窗显示状态
watch(() => props.isVisible, (newValue) => {
  if (newValue && props.tool?.detailPath) {
    nextTick(() => {
      loadContent()
    })
  }
})

// 监听语言变化，重新加载内容
watch(() => lang.value, () => {
  if (props.isVisible && props.tool?.detailPath) {
    loadContent()
  }
})

// ESC键关闭弹窗
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Escape' && props.isVisible) {
    closeModal()
  }
}

// 添加和移除键盘事件监听
watch(() => props.isVisible, (newValue) => {
  if (newValue) {
    document.addEventListener('keydown', handleKeydown)
    document.body.style.overflow = 'hidden'
  } else {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
  }
})
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style> 