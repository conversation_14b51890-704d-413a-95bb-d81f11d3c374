<template>
  <!-- Google Analytics initialization component -->
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  // Initialize Google Analytics with consent management
  if (typeof window !== 'undefined') {
    // Initialize dataLayer
    window.dataLayer = window.dataLayer || []
    
    function gtag(...args: any[]) {
      window.dataLayer.push(arguments)
    }
    
    // Make gtag globally available
    ;(window as any).gtag = gtag
    
    // Set default consent state
    gtag('consent', 'default', {
      'analytics_storage': 'denied',
      'ad_storage': 'denied'
    })
    
    gtag('js', new Date())
    
    // Check if user has already consented
    function getCookie(name: string): string | null {
      const nameEQ = name + '='
      const ca = document.cookie.split(';')
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i]
        while (c.charAt(0) === ' ') c = c.substring(1, c.length)
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
      }
      return null
    }
    
    const cookieConsent = getCookie('cookie-consent')
    const analyticsConsent = getCookie('analytics-consent')
    const advertisingConsent = getCookie('advertising-consent')
    
    if (cookieConsent === 'accepted' || analyticsConsent === 'true') {
      gtag('consent', 'update', {
        'analytics_storage': 'granted'
      })
    }
    
    if (cookieConsent === 'accepted' || advertisingConsent === 'true') {
      gtag('consent', 'update', {
        'ad_storage': 'granted'
      })
    }
    
    gtag('config', 'GA_MEASUREMENT_ID', {
      'anonymize_ip': true,
      'cookie_flags': 'SameSite=Lax;Secure'
    })
  }
})
</script>

<style scoped>
/* No styles needed for this component */
</style>
