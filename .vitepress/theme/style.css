@tailwind base;
@tailwind components;
@tailwind utilities;

/* VitePress 变量覆盖 */
:root {
  --vp-c-brand-1: #646cff;
  --vp-c-brand-2: #747bff;
  --vp-c-brand-3: #9499ff;
  --vp-c-brand-soft: rgba(100, 108, 255, 0.14);
}

.dark {
  --vp-c-bg: #1a1a1a;
  --vp-c-bg-alt: #262626;
  --vp-c-bg-elv: #262626;
  --vp-c-text-1: #ffffff;
  --vp-c-text-2: #a1a1aa;
  --vp-c-text-3: #71717a;
  --vp-c-divider: #404040;
}

/* 隐藏空的社交链接容器，去掉按钮间的分隔线 */
.VPNavBarSocialLinks {
  display: none !important;
}

.VPSocialLinks.VPNavBarSocialLinks.social-links {
  display: none !important;
}

/* 如果社交链接容器为空，则隐藏 */
.VPSocialLinks:empty {
  display: none !important;
}

/* 调整导航栏内容布局，在右侧留出空白 */
.VPNavBar .content-body {
  padding-right: 80px !important; /* 在右侧留出空白空间 */
}

/* 移动端调整 */
@media (max-width: 768px) {
  .VPNavBar .content-body {
    padding-right: 60px !important; /* 移动端留出较小空间 */
  }
  
  /* 确保桌面版主题切换按钮在移动端也显示 */
  .VPNavBarAppearance {
    display: block !important;
  }
  
  /* 隐藏汉堡菜单中的主题切换选项，避免重复 */
  .VPNavBarExtra .item.appearance {
    display: none !important;
  }
}

/* 小屏幕设备进一步调整 */
@media (max-width: 640px) {
  .VPNavBar .content-body {
    padding-right: 40px !important; /* 小屏幕留出更小空间 */
  }
}

/* 自定义组件样式 */
.tool-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700;
}

.tool-card:hover {
  @apply transform -translate-y-1;
}

.search-container {
  @apply relative w-full max-w-md mx-auto;
}

.search-input {
  @apply w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white;
}

.search-results {
  @apply absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-96 overflow-y-auto z-50;
}

.search-result-item {
  @apply px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700 last:border-b-0;
}

/* 响应式网格 */
.tool-grid {
  @apply grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

/* 分类标签 */
.category-tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

/* 工具标签 */
.tool-tag {
  @apply inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
} 