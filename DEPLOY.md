# 部署指南

本文档介绍如何将 AI 穷人导航网站部署到 Cloudflare Pages。

## 📋 部署前准备

### 1. 确保代码完整性
```bash
# 安装依赖
npm install

# 本地测试
npm run dev

# 构建测试
npm run build
```

### 2. 准备 GitHub 仓库
```bash
# 初始化 Git 仓库
git init

# 添加文件
git add .

# 提交代码
git commit -m "Initial commit: AI tools navigation site"

# 连接远程仓库
git remote add origin https://github.com/your-username/ai-tools-navigation.git

# 推送代码
git push -u origin main
```

## 🌐 Cloudflare Pages 部署

### 1. 创建 Cloudflare Pages 项目

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 点击 "Pages" 选项卡
3. 点击 "创建项目"
4. 选择 "连接到 Git"

### 2. 连接 GitHub 仓库

1. 授权 Cloudflare 访问你的 GitHub 账号
2. 选择包含项目代码的仓库
3. 点击 "开始设置"

### 3. 配置构建设置

```yaml
框架预设: VitePress
构建命令: npm install && npm run build
输出目录: .vitepress/dist
Root 目录: / (根目录)
Node.js 版本: 18 (通过 .nvmrc 文件自动检测)
```

**重要提示**：
- 确保删除了 `wrangler.toml` 文件，避免与 Pages 配置冲突
- 使用 npm 而不是 bun 进行部署，更稳定可靠
- 构建命令包含 `npm install` 确保依赖正确安装

### 4. 环境变量（可选）

如果需要，可以添加以下环境变量：

```bash
NODE_VERSION=18
VITE_BASE_URL=https://your-domain.pages.dev
```

### 5. 部署

1. 检查所有设置
2. 点击 "保存并部署"
3. 等待构建完成（通常 2-5 分钟）

## ⚙️ 自定义域名（可选）

### 1. 添加自定义域名

1. 在 Cloudflare Pages 项目中点击 "自定义域名"
2. 点击 "设置自定义域名"
3. 输入你的域名（如：ai-tools.example.com）
4. 点击 "继续"

### 2. 配置 DNS

如果域名已在 Cloudflare 管理：
- DNS 记录会自动添加

如果域名在其他服务商：
- 添加 CNAME 记录指向 `your-project.pages.dev`

## 🔄 自动部署

一旦设置完成，每次推送到 GitHub 主分支都会自动触发部署：

```bash
# 添加新工具或修改内容
git add .
git commit -m "Add new AI tool"
git push origin main

# Cloudflare Pages 会自动检测并部署
```

## 📊 监控和分析

### 1. 查看构建日志
- 在 Cloudflare Pages 控制台查看详细的构建日志
- 排查部署问题

### 2. 性能监控
- 使用 Cloudflare Analytics 查看网站访问数据
- 监控页面加载性能

### 3. 错误追踪
- 检查控制台错误
- 使用浏览器开发者工具调试

## 🛠️ 高级配置

### 1. 自定义构建命令

如果需要额外的构建步骤，可以修改 `package.json`：

```json
{
  "scripts": {
    "build": "npm run build:search && vitepress build",
    "build:search": "node scripts/build-search.js"
  }
}
```

### 2. 环境特定配置

创建 `.vitepress/config.production.ts` 用于生产环境特定设置：

```typescript
import { defineConfig } from 'vitepress'
import baseConfig from './config'

export default defineConfig({
  ...baseConfig,
  base: '/', // 生产环境基础路径
  head: [
    ...baseConfig.head,
    ['script', { async: true, src: 'https://analytics.example.com/script.js' }]
  ]
})
```

### 3. 重定向规则

在 `public/_redirects` 文件中配置重定向：

```
# 旧路径重定向
/old-tool/* /tools/:splat 301

# SPA 回退
/* /index.html 200
```

## 🔧 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本
   - 确认所有依赖已正确安装
   - 查看构建日志错误信息
   - **Bun lockfile 错误**：如果遇到 `lockfile had changes` 错误，确保：
     - 删除了 `wrangler.toml` 文件
     - 使用 npm 而不是 bun 进行部署
     - 构建命令是 `npm install && npm run build`

2. **页面显示异常**
   - 检查 Tailwind CSS 是否正确配置
   - 确认组件导入路径正确

3. **搜索功能不工作**
   - 确认 Fuse.js 依赖已安装
   - 检查搜索索引文件是否生成

### 调试步骤

```bash
# 清理缓存
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build

# 本地预览构建结果
npm run preview
```

## 📈 性能优化

### 1. 图片优化
- 使用 WebP 格式
- 添加适当的 `loading="lazy"` 属性
- 考虑使用 Cloudflare Images

### 2. 缓存策略
- 静态资源长期缓存
- HTML 文件短期缓存
- 通过 Cloudflare Pages 控制台配置缓存规则

### 3. CDN 优化
- 利用 Cloudflare 全球 CDN
- 启用 Brotli 压缩
- 优化 CSS 和 JavaScript 加载

---

部署完成后，你的 AI 穷人导航网站就可以通过 `https://your-project.pages.dev` 访问了！🎉 