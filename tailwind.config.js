/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./.vitepress/**/*.{js,ts,vue}",
    "./src/**/*.{js,ts,vue}",
    "./*.md",
    "./tools/**/*.md"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'vp-c-brand': '#646cff',
        'vp-c-brand-light': '#747bff',
        'vp-c-bg': '#ffffff',
        'vp-c-bg-alt': '#f6f6f7',
        'vp-c-bg-elv': '#ffffff',
        'vp-c-text-1': '#213547',
        'vp-c-text-2': '#476582',
        'vp-c-text-3': '#7c8b9c',
        'vp-c-divider': '#e2e8f0',
      },
      gridTemplateColumns: {
        'auto-fit': 'repeat(auto-fit, minmax(300px, 1fr))',
        'auto-fill': 'repeat(auto-fill, minmax(300px, 1fr))',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
} 